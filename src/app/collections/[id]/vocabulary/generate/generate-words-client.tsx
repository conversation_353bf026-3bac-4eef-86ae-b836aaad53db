'use client';

import { SuccessBanner } from '@/components/onboarding';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import { <PERSON><PERSON>, LoadingSpinner, Translate } from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useKeywordsContext, useTranslation } from '@/contexts';
import { useLLM } from '@/contexts/llm-context';
import { useCollections } from '@/hooks';
import { useUndoActions, createWordAddedAction } from '@/hooks/use-undo-actions';

import { RandomWord, WordDetail } from '@/models';
import { Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';
import { KeywordForm } from '../../components/keyword-form';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { GenerationLoadingState } from '../../paragraph/components/generation-loading-state';
import { WordList } from './components/word-list';

// API client function for paginated word generation
async function generateWordsPaginated(
	collectionId: string,
	keywords: string[],
	maxTerms: number,
	excludeTerms: string[],
	sourceLanguage: string,
	targetLanguage: string,
	offset: number = 0
) {
	const response = await fetch(`/api/collections/${collectionId}/vocabulary/generate`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
		},
		body: JSON.stringify({
			keywords,
			maxTerms,
			excludeTerms,
			sourceLanguage,
			targetLanguage,
			offset,
		}),
	});

	if (!response.ok) {
		const error = await response.json();
		throw new Error(error.error || 'Failed to generate words');
	}

	return response.json();
}

export function GenerateWordsClient() {
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const { generateWordDetails } = useLLM();

	const {
		currentCollection,
		loading: collectionsLoading,
		error: collectionsError,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
	} = useCollections();
	const {
		isLoading: isKeywordsLoading,
		selectedKeywords,
		keywords,
		error: keywordsError,
	} = useKeywordsContext();

	// Undo actions hook
	const { addUndoAction, getLatestAction } = useUndoActions();

	// Simple state management
	const [generatedWords, setGeneratedWords] = useState<RandomWord[]>([]);
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});
	const [isGenerating, setIsGenerating] = useState(false);
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const [currentOffset, setCurrentOffset] = useState(0);
	const [currentKeywords, setCurrentKeywords] = useState<string[]>([]);
	const [wordLoadingStates, setWordLoadingStates] = useState<
		Record<string, { adding: boolean; gettingDetail: boolean; generatingExamples: boolean }>
	>({});

	// State to track which words have been added
	const [addedWords, setAddedWords] = useState<Set<string>>(new Set());

	// Success tracking
	const [showSuccessBanner, setShowSuccessBanner] = useState(false);
	const [sessionAddedCount, setSessionAddedCount] = useState(0);
	const [initialWordCount, setInitialWordCount] = useState(0);

	// Check if any critical loading is happening
	const isCollectionsLoading = collectionsLoading.get || collectionsLoading.setCurrent;
	const isAnyLoading = isCollectionsLoading || isGenerating || isKeywordsLoading;
	const hasErrors = !!(collectionsError || keywordsError);

	// Page Guidance hook - must be called at the top level
	usePageGuidance({
		titleKey: 'words.guidance.generate.title',
		steps: [
			{ key: 'words.guidance.generate.step1' },
			{ key: 'words.guidance.generate.step2' },
			{ key: 'words.guidance.generate.step3' },
		],
		tipKey: 'words.guidance.generate.tip',
		defaultOpen: (currentCollection?.word_ids?.length || 0) === 0,
	});

	// Initialize word count tracking
	useEffect(() => {
		if (currentCollection && initialWordCount === 0) {
			setInitialWordCount(currentCollection.word_ids.length);
		}
	}, [currentCollection, initialWordCount]);

	// Error notifications
	useEffect(() => {
		if (collectionsError) {
			showError(new Error(t('collections.error')));
		}
		if (keywordsError) {
			showError(new Error(t('keywords.error')));
		}
	}, [collectionsError, keywordsError, t, showSuccess, showError]);

	// Collection safety check
	if (!currentCollection) return null;

	// Generate words function (initial load)
	const generateWords = async (keywords: string[]) => {
		setIsGenerating(true);
		try {
			const result = await generateWordsPaginated(
				currentCollection.id,
				keywords,
				20, // Initial load: 20 words
				[], // No excluded terms for initial load
				currentCollection.source_language,
				currentCollection.target_language,
				0 // Start from offset 0
			);

			setGeneratedWords(result);
			setCurrentOffset(result.length);
			setCurrentKeywords(keywords);
		} catch (error) {
			const err = error instanceof Error ? error : new Error('Failed to generate words');
			showError(new Error(t('words.generation_failed')));
		} finally {
			setIsGenerating(false);
		}
	};

	// Load more words function
	const loadMoreWords = async () => {
		if (isLoadingMore || currentKeywords.length === 0) return;

		setIsLoadingMore(true);
		try {
			const excludeTerms = generatedWords.map((word) => word.term);
			const result = await generateWordsPaginated(
				currentCollection.id,
				currentKeywords,
				20, // Load 20 more words
				excludeTerms, // Exclude already loaded words
				currentCollection.source_language,
				currentCollection.target_language,
				currentOffset
			);

			// Append new words to existing list
			setGeneratedWords((prev) => [...prev, ...result]);
			setCurrentOffset((prev) => prev + result.length);
		} catch (error) {
			const err = error instanceof Error ? error : new Error('Failed to load more words');
			showError(new Error(t('words.load_more_failed')));
		} finally {
			setIsLoadingMore(false);
		}
	};

	// Get word details
	const handleGetDetails = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.gettingDetail || detailedWords[word.term]) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], gettingDetail: true },
		}));

		try {
			const detailsList = await generateWordDetails(
				[word.term],
				currentCollection.source_language,
				currentCollection.target_language
			);
			if (detailsList && detailsList.length > 0) {
				const wordDetail = detailsList[0] as WordDetail;
				setDetailedWords((prev) => ({ ...prev, [word.term]: wordDetail }));
			} else {
				throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
			}
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			showError(new Error(t('words.detail_fetch_error')));
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], gettingDetail: false },
			}));
		}
	};

	// Generate additional examples for a word
	const handleGenerateExamples = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.generatingExamples) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], generatingExamples: true },
		}));

		try {
			// Get existing examples from detailed word if available
			const existingExamples =
				detailedWords[word.term]?.definitions
					?.flatMap((def) => def.examples || [])
					?.map((ex) => ({ EN: ex.EN, VI: ex.VI })) || [];

			const response = await fetch('/api/llm/generate-examples', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					term: word.term,
					source_language: currentCollection.source_language,
					target_language: currentCollection.target_language,
					existingExamples,
					count: 3,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Failed to generate examples');
			}

			const { examples } = await response.json();

			// Helper function to update local state
			const updateLocalWordState = (term: string, examples: any[]) => {
				setDetailedWords((prev) => {
					const currentWord = prev[term];
					if (!currentWord) return prev;

					// Add new examples to the first definition (or create one if none exists)
					const updatedDefinitions = [...(currentWord.definitions || [])];
					if (updatedDefinitions.length === 0) {
						// Create a basic definition if none exists
						updatedDefinitions.push({
							id: `temp-${Date.now()}`,
							word_id: currentWord.id,
							pos: [],
							ipa: '',
							images: [],
							explains: [],
							examples: examples.map((ex: any, index: number) => ({
								id: `temp-example-${Date.now()}-${index}`,
								EN: ex.EN,
								VI: ex.VI,
								definition_id: `temp-${Date.now()}`,
							})),
						});
					} else {
						// Add to first definition
						const newExamples = examples.map((ex: any, index: number) => ({
							id: `temp-example-${Date.now()}-${index}`,
							EN: ex.EN,
							VI: ex.VI,
							definition_id: updatedDefinitions[0].id,
						}));
						updatedDefinitions[0] = {
							...updatedDefinitions[0],
							examples: [...(updatedDefinitions[0].examples || []), ...newExamples],
						};
					}

					return {
						...prev,
						[term]: {
							...currentWord,
							definitions: updatedDefinitions,
						},
					};
				});
			};

			// If word exists in database, save examples to database
			if (detailedWords[word.term]?.id && examples.length > 0) {
				try {
					const saveResponse = await fetch(
						`/api/words/${detailedWords[word.term].id}/examples`,
						{
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ examples }),
						}
					);

					if (saveResponse.ok) {
						const { word: updatedWord } = await saveResponse.json();
						// Update with the word from database
						setDetailedWords((prev) => ({
							...prev,
							[word.term]: updatedWord,
						}));
					} else {
						// If save fails, still update local state
						updateLocalWordState(word.term, examples);
					}
				} catch (saveError) {
					console.error('Failed to save examples to database:', saveError);
					// Fallback to local state update
					updateLocalWordState(word.term, examples);
				}
			} else {
				// Word not in database yet, update local state only
				updateLocalWordState(word.term, examples);
			}

			showSuccess(
				`${t('words.examples_generated')} - ${t('words.examples_generated_description', {
					count: examples.length,
				})}`
			);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			showError(new Error(t('words.examples_generation_failed')));
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], generatingExamples: false },
			}));
		}
	};

	// Add word to collection
	const handleAddToCollection = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.adding) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], adding: true },
		}));

		try {
			let addedWordId: string | undefined;

			if (detailedWords[word.term]?.id) {
				await addWordsToCurrentCollection([detailedWords[word.term].id]);
				addedWordId = detailedWords[word.term].id;
			} else {
				const result = await addTermToCurrentCollection(
					word.term,
					currentCollection.target_language
				);
				// Find the newly added word ID by looking for the word with matching term
				if (result) {
					const addedWord = result.words.find((w) => w.term === word.term);
					addedWordId = addedWord?.id;
				}
			}

			// Mark word as added (for UI state)
			setAddedWords((prev) => new Set(prev).add(word.term));

			// Add undo action if word was successfully added
			if (addedWordId) {
				const undoAction = createWordAddedAction(
					{ term: word.term, id: addedWordId },
					currentCollection.id,
					async (wordId: string) => {
						await removeWordsFromCurrentCollection([wordId]);
						setAddedWords((prev) => {
							const newSet = new Set(prev);
							newSet.delete(word.term);
							return newSet;
						});
						await refreshCurrentCollection();
					}
				);
				addUndoAction(undoAction);
			}

			// Update session tracking
			setSessionAddedCount((prev) => prev + 1);
			setShowSuccessBanner(true);

			// Show success toast
			showSuccess(
				`${t('words.word_added')} - ${t('words.word_added_desc', { term: word.term })}`
			);

			await refreshCurrentCollection();
			setWordLoadingStates((prev) => {
				const { [word.term]: _, ...rest } = prev;
				return rest;
			});
		} catch (error) {
			showError(new Error(t('words.add_error')));
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], adding: false },
			}));
		}
	};

	// Handle undo word addition
	const handleUndoWordAddition = async (word: RandomWord) => {
		const latestAction = getLatestAction();

		// Check if the latest action is for this word
		if (
			latestAction &&
			latestAction.type === 'word_added' &&
			latestAction.data?.word?.term === word.term &&
			latestAction.data?.collectionId === currentCollection.id &&
			latestAction.undoFn
		) {
			try {
				await latestAction.undoFn();
				showSuccess(t('words.word_removed_undo', { term: word.term }));
			} catch (error) {
				showError(new Error(t('words.undo_error')));
			}
		}
	};

	// Show loading skeleton while any critical data is loading
	if (isAnyLoading && !currentCollection) return <PracticeSessionSkeleton type="paragraph" />;

	// Handle generate button click
	const handleGenerate = async () => {
		if (selectedKeywords.length === 0 || isAnyLoading || hasErrors) return;

		const keywordNames = selectedKeywords
			.map((id) => {
				const keyword = keywords.find((k) => k.id === id);
				return keyword?.content || '';
			})
			.filter(Boolean);

		if (keywordNames.length === 0) {
			showError(new Error(t('keywords.error')));
			return;
		}

		await generateWords(keywordNames);
	};

	// Helper function to get word loading state
	const getWordLoadingState = (term: string) => {
		return (
			wordLoadingStates[term] || {
				adding: false,
				gettingDetail: false,
				generatingExamples: false,
			}
		);
	};

	const currentWordCount = currentCollection.word_ids.length;

	return (
		<>
			<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
				<div className="max-w-6xl mx-auto space-y-8 py-8">
					<header className="text-center space-y-4">
						<h1 className="text-4xl font-bold text-primary dark:text-primary">
							<Translate text="words.generate_words" />
						</h1>
						<p className="text-muted-foreground dark:text-muted-foreground text-lg">
							<Translate text="words.generate_description" />
						</p>
					</header>

					{/* Success Banner */}
					{showSuccessBanner && sessionAddedCount > 0 && (
						<SuccessBanner
							collectionId={currentCollection.id}
							addedWordsCount={sessionAddedCount}
							totalWordsInCollection={currentWordCount}
							onDismiss={() => setShowSuccessBanner(false)}
						/>
					)}

					<section className="space-y-4">
						<KeywordForm />

						{/* Generate button */}
						<div className="space-y-3">
							{selectedKeywords.length > 0 && (
								<div className="text-sm text-primary/80 font-medium">
									<Translate
										text="words.selected_count"
										values={{ count: selectedKeywords.length }}
									/>
								</div>
							)}
							<Button
								className="w-full h-12 text-base font-bold rounded-2xl bg-primary text-background shadow-2xl hover:bg-primary/90 transition-all duration-200 flex gap-3 items-center justify-center"
								disabled={
									selectedKeywords.length === 0 || isAnyLoading || hasErrors
								}
								onClick={handleGenerate}
								size="sm"
								loading={isGenerating || isKeywordsLoading}
							>
								{isGenerating || isKeywordsLoading ? (
									<>
										<LoadingSpinner size="sm" />
										<Translate
											text={isGenerating ? 'words.generating' : 'ui.loading'}
										/>
									</>
								) : (
									<>
										<Sparkles className="h-6 w-6" />
										<Translate text="words.generate_words" />
									</>
								)}
							</Button>
						</div>

						<div className="grid grid-cols-1 gap-4">
							{/* Show loading state for word list operations */}
							{isGenerating ? (
								<GenerationLoadingState
									titleKey="words.generating_please_wait"
									description="Creating personalized vocabulary for your learning journey..."
								/>
							) : (
								<div className={`relative`}>
									<WordList
										words={generatedWords}
										detailedWords={detailedWords}
										onGetDetails={handleGetDetails}
										getLoadingState={getWordLoadingState}
										onAddToCollection={handleAddToCollection}
										onUndoWordAddition={handleUndoWordAddition}
										onGenerateExamples={handleGenerateExamples}
										addedWords={addedWords}
										className="mt-6"
										sourceLanguage={currentCollection.source_language}
										targetLanguage={currentCollection.target_language}
										isLoadingMore={isLoadingMore}
										onLoadMore={loadMoreWords}
									/>
								</div>
							)}
						</div>
					</section>
				</div>
			</div>
		</>
	);
}
