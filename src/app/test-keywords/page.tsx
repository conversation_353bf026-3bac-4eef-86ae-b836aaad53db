'use client';

import { useKeywordsContext } from '@/contexts';
import { Button, Card, Input, Label } from '@/components/ui';
import { useState } from 'react';

export default function TestKeywordsPage() {
	const {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		createKeyword,
		updateKeyword,
		deleteKeyword,
		syncStatus,
		syncNow,
		isLoading,
	} = useKeywordsContext();

	const [newKeywordName, setNewKeywordName] = useState('');
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editingName, setEditingName] = useState('');

	const handleCreateKeyword = async () => {
		if (!newKeywordName.trim()) return;
		await createKeyword(newKeywordName.trim());
		setNewKeywordName('');
	};

	const handleUpdateKeyword = async (id: string) => {
		if (!editingName.trim()) return;
		await updateKeyword(id, editingName.trim());
		setEditingId(null);
		setEditingName('');
	};

	const handleDeleteKeyword = async (id: string) => {
		await deleteKeyword(id);
	};

	const toggleSelection = (id: string) => {
		if (selectedKeywords.includes(id)) {
			setSelectedKeywords(selectedKeywords.filter((kId) => kId !== id));
		} else {
			setSelectedKeywords([...selectedKeywords, id]);
		}
	};

	return (
		<div className="container mx-auto p-6 space-y-6">
			<h1 className="text-3xl font-bold">Keyword System Test</h1>

			{/* Sync Status */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-2">Sync Status</h2>
				<div className="space-y-2">
					<p>Pending Actions: {syncStatus.pendingActions}</p>
					<p>Is Syncing: {syncStatus.isSync ? 'Yes' : 'No'}</p>
					<p>Has Unsynced Changes: {syncStatus.hasUnsyncedChanges ? 'Yes' : 'No'}</p>
					<p>Is Loading: {isLoading ? 'Yes' : 'No'}</p>
					<Button onClick={syncNow} size="sm">
						Force Sync Now
					</Button>
				</div>
			</Card>

			{/* Create Keyword */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-2">Create Keyword</h2>
				<div className="flex gap-2">
					<Input
						value={newKeywordName}
						onChange={(e) => setNewKeywordName(e.target.value)}
						placeholder="Enter keyword name"
						onKeyDown={(e) => {
							if (e.key === 'Enter') {
								handleCreateKeyword();
							}
						}}
					/>
					<Button onClick={handleCreateKeyword} disabled={!newKeywordName.trim()}>
						Create
					</Button>
				</div>
			</Card>

			{/* Keywords List */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-2">Keywords ({keywords.length})</h2>
				<div className="space-y-2">
					{keywords.map((keyword) => (
						<div
							key={keyword.id}
							className={`p-3 border rounded-lg ${
								selectedKeywords.includes(keyword.id)
									? 'bg-blue-50 border-blue-300'
									: 'bg-gray-50 border-gray-300'
							}`}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<input
										type="checkbox"
										checked={selectedKeywords.includes(keyword.id)}
										onChange={() => toggleSelection(keyword.id)}
									/>
									{editingId === keyword.id ? (
										<div className="flex gap-2">
											<Input
												value={editingName}
												onChange={(e) => setEditingName(e.target.value)}
												onKeyDown={(e) => {
													if (e.key === 'Enter') {
														handleUpdateKeyword(keyword.id);
													}
													if (e.key === 'Escape') {
														setEditingId(null);
														setEditingName('');
													}
												}}
												size="sm"
											/>
											<Button
												onClick={() => handleUpdateKeyword(keyword.id)}
												size="sm"
											>
												Save
											</Button>
											<Button
												onClick={() => {
													setEditingId(null);
													setEditingName('');
												}}
												variant="outline"
												size="sm"
											>
												Cancel
											</Button>
										</div>
									) : (
										<span className="font-medium">{keyword.content}</span>
									)}
								</div>
								<div className="flex gap-2">
									<Button
										onClick={() => {
											setEditingId(keyword.id);
											setEditingName(keyword.content);
										}}
										variant="outline"
										size="sm"
									>
										Edit
									</Button>
									<Button
										onClick={() => handleDeleteKeyword(keyword.id)}
										variant="destructive"
										size="sm"
									>
										Delete
									</Button>
								</div>
							</div>
							<div className="text-xs text-gray-500 mt-1">
								ID: {keyword.id} | User: {keyword.user_id}
							</div>
						</div>
					))}
					{keywords.length === 0 && (
						<p className="text-gray-500 text-center py-4">No keywords found</p>
					)}
				</div>
			</Card>

			{/* Selected Keywords */}
			<Card className="p-4">
				<h2 className="text-xl font-semibold mb-2">
					Selected Keywords ({selectedKeywords.length})
				</h2>
				<div className="flex flex-wrap gap-2">
					{selectedKeywords.map((id) => {
						const keyword = keywords.find((k) => k.id === id);
						return (
							<span
								key={id}
								className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
							>
								{keyword?.content || id}
							</span>
						);
					})}
					{selectedKeywords.length === 0 && (
						<p className="text-gray-500">No keywords selected</p>
					)}
				</div>
			</Card>
		</div>
	);
}
