import { KeywordWithDetail } from '@/models';

const KEYWORDS_STORAGE_KEY = 'vocab-keywords';
const SELECTED_KEYWORDS_STORAGE_KEY = 'vocab-selected-keywords';
const KEYWORDS_SYNC_QUEUE_KEY = 'vocab-keywords-sync-queue';

export interface KeywordSyncAction {
	id: string;
	type: 'create' | 'update' | 'delete';
	data?: {
		name?: string;
		keywordId?: string;
	};
	timestamp: number;
}

export interface KeywordStorageData {
	keywords: KeywordWithDetail[];
	lastSyncTimestamp: number;
	version: number;
}

/**
 * Utility class for managing keywords in localStorage with background sync
 */
export class KeywordStorage {
	private static instance: KeywordStorage;

	private constructor() {}

	static getInstance(): KeywordStorage {
		if (!KeywordStorage.instance) {
			KeywordStorage.instance = new KeywordStorage();
		}
		return KeywordStorage.instance;
	}

	/**
	 * Get keywords from localStorage
	 */
	getKeywords(): KeywordWithDetail[] {
		try {
			const stored = localStorage.getItem(KEYWORDS_STORAGE_KEY);
			if (!stored) return [];

			const data: KeywordStorageData = JSON.parse(stored);
			return data.keywords || [];
		} catch (error) {
			console.error('Failed to get keywords from localStorage:', error);
			return [];
		}
	}

	/**
	 * Save keywords to localStorage
	 */
	saveKeywords(keywords: KeywordWithDetail[]): void {
		try {
			const data: KeywordStorageData = {
				keywords,
				lastSyncTimestamp: Date.now(),
				version: 1,
			};
			localStorage.setItem(KEYWORDS_STORAGE_KEY, JSON.stringify(data));
		} catch (error) {
			console.error('Failed to save keywords to localStorage:', error);
		}
	}

	/**
	 * Get selected keywords from localStorage
	 */
	getSelectedKeywords(): string[] {
		try {
			const stored = localStorage.getItem(SELECTED_KEYWORDS_STORAGE_KEY);
			return stored ? JSON.parse(stored) : [];
		} catch (error) {
			console.error('Failed to get selected keywords from localStorage:', error);
			return [];
		}
	}

	/**
	 * Save selected keywords to localStorage
	 */
	saveSelectedKeywords(selectedIds: string[]): void {
		try {
			localStorage.setItem(SELECTED_KEYWORDS_STORAGE_KEY, JSON.stringify(selectedIds));
		} catch (error) {
			console.error('Failed to save selected keywords to localStorage:', error);
		}
	}

	/**
	 * Add keyword locally (optimistic update)
	 */
	addKeywordLocally(keyword: KeywordWithDetail): void {
		const keywords = this.getKeywords();
		const updatedKeywords = [...keywords, keyword];
		this.saveKeywords(updatedKeywords);
	}

	/**
	 * Update keyword locally (optimistic update)
	 */
	updateKeywordLocally(id: string, updates: Partial<KeywordWithDetail>): void {
		const keywords = this.getKeywords();
		const updatedKeywords = keywords.map((keyword) =>
			keyword.id === id ? { ...keyword, ...updates } : keyword
		);
		this.saveKeywords(updatedKeywords);
	}

	/**
	 * Delete keyword locally (optimistic update)
	 */
	deleteKeywordLocally(id: string): void {
		const keywords = this.getKeywords();
		const updatedKeywords = keywords.filter((keyword) => keyword.id !== id);
		this.saveKeywords(updatedKeywords);

		// Also remove from selected keywords
		const selectedKeywords = this.getSelectedKeywords();
		const updatedSelected = selectedKeywords.filter((keywordId) => keywordId !== id);
		this.saveSelectedKeywords(updatedSelected);
	}

	/**
	 * Get sync queue from localStorage
	 */
	getSyncQueue(): KeywordSyncAction[] {
		try {
			const stored = localStorage.getItem(KEYWORDS_SYNC_QUEUE_KEY);
			return stored ? JSON.parse(stored) : [];
		} catch (error) {
			console.error('Failed to get sync queue from localStorage:', error);
			return [];
		}
	}

	/**
	 * Add action to sync queue
	 */
	addToSyncQueue(action: Omit<KeywordSyncAction, 'timestamp'>): void {
		try {
			const queue = this.getSyncQueue();
			const newAction: KeywordSyncAction = {
				...action,
				timestamp: Date.now(),
			};
			queue.push(newAction);
			localStorage.setItem(KEYWORDS_SYNC_QUEUE_KEY, JSON.stringify(queue));
		} catch (error) {
			console.error('Failed to add action to sync queue:', error);
		}
	}

	/**
	 * Remove action from sync queue
	 */
	removeFromSyncQueue(actionId: string): void {
		try {
			const queue = this.getSyncQueue();
			const updatedQueue = queue.filter((action) => action.id !== actionId);
			localStorage.setItem(KEYWORDS_SYNC_QUEUE_KEY, JSON.stringify(updatedQueue));
		} catch (error) {
			console.error('Failed to remove action from sync queue:', error);
		}
	}

	/**
	 * Clear sync queue
	 */
	clearSyncQueue(): void {
		try {
			localStorage.removeItem(KEYWORDS_SYNC_QUEUE_KEY);
		} catch (error) {
			console.error('Failed to clear sync queue:', error);
		}
	}

	/**
	 * Clear all keyword data from localStorage
	 */
	clearAll(): void {
		try {
			localStorage.removeItem(KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(SELECTED_KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(KEYWORDS_SYNC_QUEUE_KEY);
		} catch (error) {
			console.error('Failed to clear keyword data from localStorage:', error);
		}
	}

	/**
	 * Generate unique ID for sync actions
	 */
	generateActionId(): string {
		return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}
}

export const keywordStorage = KeywordStorage.getInstance();
